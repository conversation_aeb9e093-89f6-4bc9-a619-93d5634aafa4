<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [GD32_Xifeng\2025950859.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image GD32_Xifeng\2025950859.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6220000: Last Updated: Mon Jun 16 23:04:52 2025
<BR><P>
<H3>Maximum Stack Usage =       1752 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
uart_task &rArr; handle_sampling_output &rArr; data_storage_write_hidedata &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[4f]">CAN1_RX0_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4f]">CAN1_RX0_IRQHandler</a><BR>
 <LI><a href="#[35]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[35]">BusFault_Handler</a><BR>
 <LI><a href="#[33]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[33]">HardFault_Handler</a><BR>
 <LI><a href="#[34]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[34]">MemManage_Handler</a><BR>
 <LI><a href="#[32]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[32]">NMI_Handler</a><BR>
 <LI><a href="#[36]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[36]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[9c]">ADC_DMAConvCplt</a> from stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) referenced 2 times from stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
 <LI><a href="#[9e]">ADC_DMAError</a> from stm32f4xx_hal_adc.o(.text.ADC_DMAError) referenced 2 times from stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
 <LI><a href="#[9d]">ADC_DMAHalfConvCplt</a> from stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) referenced 2 times from stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
 <LI><a href="#[4d]">ADC_IRQHandler</a> from stm32f4xx_it.o(.text.ADC_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[35]">BusFault_Handler</a> from stm32f4xx_it.o(.text.BusFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4f]">CAN1_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[50]">CAN1_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[51]">CAN1_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4e]">CAN1_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7b]">CAN2_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7c]">CAN2_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7d]">CAN2_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7a]">CAN2_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[89]">DCMI_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[46]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[47]">DMA1_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[48]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[49]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4a]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4b]">DMA1_Stream5_IRQHandler</a> from stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4c]">DMA1_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6a]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[94]">DMA2D_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[73]">DMA2_Stream0_IRQHandler</a> from stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[74]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[75]">DMA2_Stream2_IRQHandler</a> from stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[76]">DMA2_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[77]">DMA2_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7f]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[80]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[81]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[38]">DebugMon_Handler</a> from stm32f4xx_it.o(.text.DebugMon_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[78]">ETH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[79]">ETH_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[41]">EXTI0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[63]">EXTI15_10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[42]">EXTI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[43]">EXTI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[44]">EXTI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[45]">EXTI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[52]">EXTI9_5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3f]">FLASH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6b]">FMC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8b]">FPU_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8a]">HASH_RNG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[33]">HardFault_Handler</a> from stm32f4xx_it.o(.text.HardFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5b]">I2C1_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5a]">I2C1_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5d]">I2C2_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5c]">I2C2_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[84]">I2C3_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[83]">I2C3_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[93]">LTDC_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[92]">LTDC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[34]">MemManage_Handler</a> from stm32f4xx_it.o(.text.MemManage_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[32]">NMI_Handler</a> from stm32f4xx_it.o(.text.NMI_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7e]">OTG_FS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[65]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[86]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[85]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[88]">OTG_HS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[87]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3c]">PVD_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[39]">PendSV_Handler</a> from stm32f4xx_it.o(.text.PendSV_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[40]">RCC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[64]">RTC_Alarm_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3e]">RTC_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[31]">Reset_Handler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[91]">SAI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6c]">SDIO_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[ae]">SD_initialize</a> from sd_diskio.o(.text.SD_initialize) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[b2]">SD_ioctl</a> from sd_diskio.o(.text.SD_ioctl) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[b0]">SD_read</a> from sd_diskio.o(.text.SD_read) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[af]">SD_status</a> from sd_diskio.o(.text.SD_status) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[b1]">SD_write</a> from sd_diskio.o(.text.SD_write) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[5e]">SPI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5f]">SPI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6e]">SPI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8e]">SPI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8f]">SPI5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[90]">SPI6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[37]">SVC_Handler</a> from stm32f4xx_it.o(.text.SVC_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3a]">SysTick_Handler</a> from stm32f4xx_it.o(.text.SysTick_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[96]">SystemInit</a> from system_stm32f4xx.o(.text.SystemInit) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[3d]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[53]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[56]">TIM1_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[55]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[54]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[57]">TIM2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[58]">TIM3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[59]">TIM4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6d]">TIM5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[71]">TIM6_DAC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[72]">TIM7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[66]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[69]">TIM8_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[68]">TIM8_TRG_COM_TIM14_IRQHandler</a> from stm32f4xx_it.o(.text.TIM8_TRG_COM_TIM14_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[67]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6f]">UART4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[70]">UART5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8c]">UART7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8d]">UART8_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a2]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) referenced 2 times from stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
 <LI><a href="#[a1]">UART_DMAError</a> from stm32f4xx_hal_uart.o(.text.UART_DMAError) referenced 2 times from stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
 <LI><a href="#[9f]">UART_DMAReceiveCplt</a> from stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) referenced 2 times from stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
 <LI><a href="#[a0]">UART_DMARxHalfCplt</a> from stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) referenced 2 times from stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
 <LI><a href="#[60]">USART1_IRQHandler</a> from stm32f4xx_it.o(.text.USART1_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[61]">USART2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[62]">USART3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[82]">USART6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[36]">UsageFault_Handler</a> from stm32f4xx_it.o(.text.UsageFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3b]">WWDG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[16]">WouoUI_ConfWinPageIn</a> from wououi_win.o(.text.WouoUI_ConfWinPageIn) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[17]">WouoUI_ConfWinPageInParaInit</a> from wououi_win.o(.text.WouoUI_ConfWinPageInParaInit) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[1a]">WouoUI_ConfWinPageIndicatorCtrl</a> from wououi_win.o(.text.WouoUI_ConfWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[19]">WouoUI_ConfWinPageReact</a> from wououi_win.o(.text.WouoUI_ConfWinPageReact) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[18]">WouoUI_ConfWinPageShow</a> from wououi_win.o(.text.WouoUI_ConfWinPageShow) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[5]">WouoUI_FuncDoNothing</a> from wououi.o(.text.WouoUI_FuncDoNothing) referenced 12 times from wououi.o(.data.default_ui)
 <LI><a href="#[c]">WouoUI_FuncDoNothingRetTrue</a> from wououi.o(.text.WouoUI_FuncDoNothingRetTrue) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[6]">WouoUI_ListPageIn</a> from wououi_page.o(.text.WouoUI_ListPageIn) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[7]">WouoUI_ListPageInParaInit</a> from wououi_page.o(.text.WouoUI_ListPageInParaInit) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[a]">WouoUI_ListPageIndicatorCtrl</a> from wououi_page.o(.text.WouoUI_ListPageIndicatorCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[9]">WouoUI_ListPageReact</a> from wououi_page.o(.text.WouoUI_ListPageReact) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[b]">WouoUI_ListPageScrollBarCtrl</a> from wououi_page.o(.text.WouoUI_ListPageScrollBarCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[8]">WouoUI_ListPageShow</a> from wououi_page.o(.text.WouoUI_ListPageShow) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[25]">WouoUI_ListWinPageIn</a> from wououi_win.o(.text.WouoUI_ListWinPageIn) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[26]">WouoUI_ListWinPageInParaInit</a> from wououi_win.o(.text.WouoUI_ListWinPageInParaInit) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[29]">WouoUI_ListWinPageIndicatorCtrl</a> from wououi_win.o(.text.WouoUI_ListWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[28]">WouoUI_ListWinPageReact</a> from wououi_win.o(.text.WouoUI_ListWinPageReact) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[2a]">WouoUI_ListWinPageScrollBarCtrl</a> from wououi_win.o(.text.WouoUI_ListWinPageScrollBarCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[27]">WouoUI_ListWinPageShow</a> from wououi_win.o(.text.WouoUI_ListWinPageShow) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[11]">WouoUI_MsgWinPageIn</a> from wououi_win.o(.text.WouoUI_MsgWinPageIn) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[12]">WouoUI_MsgWinPageInParaInit</a> from wououi_win.o(.text.WouoUI_MsgWinPageInParaInit) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[15]">WouoUI_MsgWinPageIndicatorCtrl</a> from wououi_win.o(.text.WouoUI_MsgWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[14]">WouoUI_MsgWinPageReact</a> from wououi_win.o(.text.WouoUI_MsgWinPageReact) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[13]">WouoUI_MsgWinPageShow</a> from wououi_win.o(.text.WouoUI_MsgWinPageShow) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[20]">WouoUI_SpinWinPageIn</a> from wououi_win.o(.text.WouoUI_SpinWinPageIn) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[21]">WouoUI_SpinWinPageInParaInit</a> from wououi_win.o(.text.WouoUI_SpinWinPageInParaInit) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[24]">WouoUI_SpinWinPageIndicatorCtrl</a> from wououi_win.o(.text.WouoUI_SpinWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[23]">WouoUI_SpinWinPageReact</a> from wououi_win.o(.text.WouoUI_SpinWinPageReact) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[22]">WouoUI_SpinWinPageShow</a> from wououi_win.o(.text.WouoUI_SpinWinPageShow) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[0]">WouoUI_TitlePageIn</a> from wououi_page.o(.text.WouoUI_TitlePageIn) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[1]">WouoUI_TitlePageInParaInit</a> from wououi_page.o(.text.WouoUI_TitlePageInParaInit) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[4]">WouoUI_TitlePageIndicatorCtrl</a> from wououi_page.o(.text.WouoUI_TitlePageIndicatorCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[3]">WouoUI_TitlePageReact</a> from wououi_page.o(.text.WouoUI_TitlePageReact) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[2]">WouoUI_TitlePageShow</a> from wououi_page.o(.text.WouoUI_TitlePageShow) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[1b]">WouoUI_ValWinPageIn</a> from wououi_win.o(.text.WouoUI_ValWinPageIn) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[1c]">WouoUI_ValWinPageInParaInit</a> from wououi_win.o(.text.WouoUI_ValWinPageInParaInit) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[1f]">WouoUI_ValWinPageIndicatorCtrl</a> from wououi_win.o(.text.WouoUI_ValWinPageIndicatorCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[1e]">WouoUI_ValWinPageReact</a> from wououi_win.o(.text.WouoUI_ValWinPageReact) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[1d]">WouoUI_ValWinPageShow</a> from wououi_win.o(.text.WouoUI_ValWinPageShow) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[d]">WouoUI_WavePageInParaInit</a> from wououi_page.o(.text.WouoUI_WavePageInParaInit) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[10]">WouoUI_WavePageIndicatorCtrl</a> from wououi_page.o(.text.WouoUI_WavePageIndicatorCtrl) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[f]">WouoUI_WavePageReact</a> from wououi_page.o(.text.WouoUI_WavePageReact) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[e]">WouoUI_WavePageShow</a> from wououi_page.o(.text.WouoUI_WavePageShow) referenced 2 times from wououi.o(.data.default_ui)
 <LI><a href="#[97]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[99]">_sbackspace</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[9a]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[98]">_sgetc</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[ad]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[ac]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[2c]">adc_task</a> from adc_app.o(.text.adc_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[2d]">btn_task</a> from btn_app.o(.text.btn_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[ab]">fputc</a> from usart.o(.text.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[9b]">isspace</a> from isspace_o.o(.text) referenced 2 times from scanf_char.o(.text)
 <LI><a href="#[2b]">led_task</a> from led_app.o(.text.led_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[95]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[2f]">oled_task</a> from oled_app.o(.text.oled_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[a3]">prv_btn_event</a> from btn_app.o(.text.prv_btn_event) referenced 2 times from btn_app.o(.text.app_btn_init)
 <LI><a href="#[a4]">prv_btn_get_state</a> from btn_app.o(.text.prv_btn_get_state) referenced 2 times from btn_app.o(.text.app_btn_init)
 <LI><a href="#[30]">sampling_task</a> from sampling_control.o(.text.sampling_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[b5]">u8g2_draw_l90_r0</a> from u8g2_setup.o(.text.u8g2_draw_l90_r0) referenced from u8g2_setup.o(.rodata.u8g2_cb_r0)
 <LI><a href="#[a7]">u8g2_font_calc_vref_font</a> from u8g2_font.o(.text.u8g2_font_calc_vref_font) referenced 2 times from u8g2_font.o(.text.u8g2_SetFontPosBaseline)
 <LI><a href="#[a6]">u8g2_gpio_and_delay_stm32</a> from oled_app.o(.text.u8g2_gpio_and_delay_stm32) referenced 2 times from main.o(.text.main)
 <LI><a href="#[aa]">u8g2_ll_hvline_vertical_top_lsb</a> from u8g2_ll_hvline.o(.text.u8g2_ll_hvline_vertical_top_lsb) referenced 2 times from u8g2_d_setup.o(.text.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
 <LI><a href="#[b3]">u8g2_update_dimension_r0</a> from u8g2_setup.o(.text.u8g2_update_dimension_r0) referenced from u8g2_setup.o(.rodata.u8g2_cb_r0)
 <LI><a href="#[b4]">u8g2_update_page_win_r0</a> from u8g2_setup.o(.text.u8g2_update_page_win_r0) referenced from u8g2_setup.o(.rodata.u8g2_cb_r0)
 <LI><a href="#[a5]">u8x8_byte_hw_i2c</a> from oled_app.o(.text.u8x8_byte_hw_i2c) referenced 2 times from main.o(.text.main)
 <LI><a href="#[a9]">u8x8_cad_ssd13xx_fast_i2c</a> from u8x8_cad.o(.text.u8x8_cad_ssd13xx_fast_i2c) referenced 2 times from u8g2_d_setup.o(.text.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
 <LI><a href="#[a8]">u8x8_d_ssd1306_128x32_univision</a> from u8x8_d_ssd1306_128x32.o(.text.u8x8_d_ssd1306_128x32_univision) referenced 2 times from u8g2_d_setup.o(.text.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
 <LI><a href="#[2e]">uart_task</a> from usart_app.o(.text.uart_task) referenced 2 times from scheduler.o(.data.scheduler_task)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[97]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[22b]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[b6]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[d5]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[22c]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[22d]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[22e]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[22f]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[230]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[31]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[89]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[94]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[84]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[93]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[92]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[86]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[88]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[91]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8f]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8c]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8d]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[82]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[b8]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[bb]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_line
</UL>

<P><STRONG><a name="[9b]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 2]<UL><LI> strtod.o(.text)
<LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[231]"></a>___aeabi_memcpy8$move</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[207]"></a>__aeabi_memcpy</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, memmovea.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
</UL>

<P><STRONG><a name="[232]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[1ed]"></a>__aeabi_memmove</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memmove
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_line
</UL>

<P><STRONG><a name="[233]"></a>__aeabi_memmove4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[234]"></a>__aeabi_memmove8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[bd]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[235]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[236]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[bc]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
</UL>

<P><STRONG><a name="[10f]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[237]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[be]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[1bb]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_line
</UL>

<P><STRONG><a name="[1ee]"></a>strchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, strchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_line
</UL>

<P><STRONG><a name="[174]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_line
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_unhide_conversion
</UL>

<P><STRONG><a name="[1fc]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[184]"></a>memchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, memchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageReact
</UL>

<P><STRONG><a name="[1ba]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_line
</UL>

<P><STRONG><a name="[19a]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f_str
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_flash_status
</UL>

<P><STRONG><a name="[1fd]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[bf]"></a>__0sscanf</STRONG> (Thumb, 48 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_unhide_conversion
</UL>

<P><STRONG><a name="[c1]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[d6]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[c7]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[1b2]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configread_command
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configsave_command
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_command
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_command
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_overlimit
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_sample
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_unhide_conversion
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[238]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[22a]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[ba]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[239]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[b9]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[23a]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[c2]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>

<P><STRONG><a name="[c0]"></a>__vfscanf_char</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[98]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> strtod.o(.text)
<LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[99]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> strtod.o(.text)
<LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[cb]"></a>__strtof_int</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, strtof.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = __strtof_int &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_strtof
</UL>

<P><STRONG><a name="[23b]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[cd]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[d1]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[d2]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[c5]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[c6]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[c4]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[d3]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[227]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[c9]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[b7]"></a>__scatterload</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[23c]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[ce]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[23d]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[ca]"></a>__vfscanf</STRONG> (Thumb, 808 bytes, Stack size 88 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[cc]"></a>__strtod_int</STRONG> (Thumb, 94 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtof_int
</UL>

<P><STRONG><a name="[d4]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[23e]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[d0]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[cf]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[23f]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[240]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[4d]"></a>ADC_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ADC_IRQHandler &rArr; HAL_ADC_IRQHandler &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[dc]"></a>BSP_SD_GetCardInfo</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, bsp_driver_sd.o(.text.BSP_SD_GetCardInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = BSP_SD_GetCardInfo &rArr; HAL_SD_GetCardInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ioctl
</UL>

<P><STRONG><a name="[de]"></a>BSP_SD_GetCardState</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, bsp_driver_sd.o(.text.BSP_SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = BSP_SD_GetCardState &rArr; HAL_SD_GetCardState
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_write
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_read
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_status
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_initialize
</UL>

<P><STRONG><a name="[e0]"></a>BSP_SD_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, bsp_driver_sd.o(.text.BSP_SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = BSP_SD_Init &rArr; HAL_SD_ConfigWideBusOperation &rArr; SD_FindSCR &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_IsDetected
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_initialize
</UL>

<P><STRONG><a name="[e1]"></a>BSP_SD_IsDetected</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, bsp_driver_sd.o(.text.BSP_SD_IsDetected))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = BSP_SD_IsDetected
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[e4]"></a>BSP_SD_ReadBlocks</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, bsp_driver_sd.o(.text.BSP_SD_ReadBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = BSP_SD_ReadBlocks &rArr; HAL_SD_ReadBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_read
</UL>

<P><STRONG><a name="[e6]"></a>BSP_SD_WriteBlocks</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, bsp_driver_sd.o(.text.BSP_SD_WriteBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = BSP_SD_WriteBlocks &rArr; HAL_SD_WriteBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_write
</UL>

<P><STRONG><a name="[35]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA1_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA2_Stream0_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA2_Stream2_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[f0]"></a>Error_Handler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, main.o(.text.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM14_Init
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[151]"></a>FATFS_LinkDriver</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, ff_gen_drv.o(.text.FATFS_LinkDriver))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FATFS_LinkDriver
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
</UL>

<P><STRONG><a name="[ea]"></a>HAL_ADCEx_InjectedConvCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[14c]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 368 bytes, Stack size 20 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_ADC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[d9]"></a>HAL_ADC_ConvCpltCallback</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, adc_app.o(.text.HAL_ADC_ConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[da]"></a>HAL_ADC_ConvHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAHalfConvCplt
</UL>

<P><STRONG><a name="[d8]"></a>HAL_ADC_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAError
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[db]"></a>HAL_ADC_IRQHandler</STRONG> (Thumb, 310 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_ADC_IRQHandler &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_LevelOutOfWindowCallback
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConvCpltCallback
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>

<P><STRONG><a name="[ec]"></a>HAL_ADC_Init</STRONG> (Thumb, 348 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[eb]"></a>HAL_ADC_LevelOutOfWindowCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[ed]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 236 bytes, Stack size 48 bytes, adc.o(.text.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[f3]"></a>HAL_ADC_Start_DMA</STRONG> (Thumb, 422 bytes, Stack size 24 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_tim_dma_init
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[e9]"></a>HAL_ADC_Stop_DMA</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>

<P><STRONG><a name="[14e]"></a>HAL_DAC_ConfigChannel</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DAC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[f6]"></a>HAL_DAC_Init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f4xx_hal_dac.o(.text.HAL_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[f7]"></a>HAL_DAC_MspInit</STRONG> (Thumb, 182 bytes, Stack size 48 bytes, dac.o(.text.HAL_DAC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
</UL>

<P><STRONG><a name="[f5]"></a>HAL_DMA_Abort</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[143]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[e8]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 452 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream5_IRQHandler
</UL>

<P><STRONG><a name="[ef]"></a>HAL_DMA_Init</STRONG> (Thumb, 354 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>

<P><STRONG><a name="[f4]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 162 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>

<P><STRONG><a name="[f9]"></a>HAL_Delay</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32f4xx_hal.o(.text.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_gpio_and_delay_stm32
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[ee]"></a>HAL_GPIO_Init</STRONG> (Thumb, 414 bytes, Stack size 48 bytes, stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[204]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_get_state
</UL>

<P><STRONG><a name="[153]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
</UL>

<P><STRONG><a name="[f8]"></a>HAL_GetTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_start
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_command
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_task
</UL>

<P><STRONG><a name="[155]"></a>HAL_I2CEx_ConfigAnalogFilter</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigAnalogFilter))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[156]"></a>HAL_I2CEx_ConfigDigitalFilter</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigDigitalFilter))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[fa]"></a>HAL_I2C_Init</STRONG> (Thumb, 356 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[fd]"></a>HAL_I2C_Master_Transmit</STRONG> (Thumb, 476 bytes, Stack size 40 bytes, stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_I2C_Master_Transmit &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_hw_i2c
</UL>

<P><STRONG><a name="[102]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 348 bytes, Stack size 40 bytes, stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[fb]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, i2c.o(.text.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[165]"></a>HAL_IncTick</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[104]"></a>HAL_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_hal.o(.text.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[106]"></a>HAL_InitTick</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_hal.o(.text.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[107]"></a>HAL_MspInit</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(.text.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[f2]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[f1]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[105]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[109]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 576 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>

<P><STRONG><a name="[10a]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 356 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[fc]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[168]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[10b]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[10c]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 940 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[1b1]"></a>HAL_RTC_GetDate</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_GetDate))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RTC_GetDate
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_filename
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_print_current_time
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_overlimit
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_sample
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[1b0]"></a>HAL_RTC_GetTime</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_GetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RTC_GetTime
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_filename
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_print_current_time
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_overlimit
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_sample
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[10d]"></a>HAL_RTC_Init</STRONG> (Thumb, 252 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
</UL>

<P><STRONG><a name="[10e]"></a>HAL_RTC_MspInit</STRONG> (Thumb, 76 bytes, Stack size 56 bytes, rtc.o(.text.HAL_RTC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[110]"></a>HAL_RTC_SetDate</STRONG> (Thumb, 356 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_SetDate))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RTC_SetDate
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
</UL>

<P><STRONG><a name="[111]"></a>HAL_RTC_SetTime</STRONG> (Thumb, 424 bytes, Stack size 32 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_SetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RTC_SetTime
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
</UL>

<P><STRONG><a name="[e3]"></a>HAL_SD_ConfigWideBusOperation</STRONG> (Thumb, 310 bytes, Stack size 48 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_ConfigWideBusOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SD_ConfigWideBusOperation &rArr; SD_FindSCR &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[120]"></a>HAL_SD_GetCardCSD</STRONG> (Thumb, 390 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_GetCardCSD))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SD_GetCardCSD
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[dd]"></a>HAL_SD_GetCardInfo</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_GetCardInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SD_GetCardInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardInfo
</UL>

<P><STRONG><a name="[df]"></a>HAL_SD_GetCardState</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SD_GetCardState
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>

<P><STRONG><a name="[e2]"></a>HAL_SD_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SD_Init &rArr; HAL_SD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[11a]"></a>HAL_SD_InitCard</STRONG> (Thumb, 540 bytes, Stack size 80 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_InitCard))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_SD_InitCard &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardCSD
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetPowerState
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_PowerState_ON
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[119]"></a>HAL_SD_MspInit</STRONG> (Thumb, 174 bytes, Stack size 56 bytes, sdio.o(.text.HAL_SD_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_SD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[e5]"></a>HAL_SD_ReadBlocks</STRONG> (Thumb, 568 bytes, Stack size 56 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_ReadBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_SD_ReadBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_ReadBlocks
</UL>

<P><STRONG><a name="[e7]"></a>HAL_SD_WriteBlocks</STRONG> (Thumb, 566 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_WriteBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SD_WriteBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_WriteFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_WriteBlocks
</UL>

<P><STRONG><a name="[12d]"></a>HAL_SPI_Init</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
</UL>

<P><STRONG><a name="[12e]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, spi.o(.text.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[12f]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 838 bytes, Stack size 40 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
</UL>

<P><STRONG><a name="[108]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[139]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13b]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[15c]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 186 bytes, Stack size 8 bytes, stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[131]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM14_Init
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[132]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, tim.o(.text.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[193]"></a>HAL_TIM_Base_Start</STRONG> (Thumb, 162 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start))
<BR><BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_tim_dma_init
</UL>

<P><STRONG><a name="[196]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 170 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT))
<BR><BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
</UL>

<P><STRONG><a name="[15b]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 416 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIM_ConfigClockSource
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[135]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[134]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 334 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM8_TRG_COM_TIM14_IRQHandler
</UL>

<P><STRONG><a name="[136]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[137]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[138]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, btn_app.o(.text.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13a]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13c]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 484 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[13d]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, usart_app.o(.text.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[13e]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 540 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_UART_DMAStop &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[144]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[140]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 1392 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[145]"></a>HAL_UART_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[146]"></a>HAL_UART_MspInit</STRONG> (Thumb, 194 bytes, Stack size 40 bytes, usart.o(.text.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[149]"></a>HAL_UART_Receive_IT</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT))
<BR><BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>

<P><STRONG><a name="[148]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, usart_app.o(.text.HAL_UART_RxCpltCallback))
<BR><BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[167]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[14a]"></a>HAL_UART_Transmit</STRONG> (Thumb, 402 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[142]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[33]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[14b]"></a>MX_ADC1_Init</STRONG> (Thumb, 140 bytes, Stack size 32 bytes, adc.o(.text.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14d]"></a>MX_DAC_Init</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, dac.o(.text.MX_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_DAC_Init &rArr; HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConfigChannel
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14f]"></a>MX_DMA_Init</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, dma.o(.text.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[150]"></a>MX_FATFS_Init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, fatfs.o(.text.MX_FATFS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = MX_FATFS_Init &rArr; FATFS_LinkDriver
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FATFS_LinkDriver
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[152]"></a>MX_GPIO_Init</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, gpio.o(.text.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[154]"></a>MX_I2C1_Init</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, i2c.o(.text.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigDigitalFilter
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigAnalogFilter
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[157]"></a>MX_RTC_Init</STRONG> (Thumb, 140 bytes, Stack size 32 bytes, rtc.o(.text.MX_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_RTC_Init &rArr; HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f1]"></a>MX_SDIO_SD_Init</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, sdio.o(.text.MX_SDIO_SD_Init))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[158]"></a>MX_SPI2_Init</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, spi.o(.text.MX_SPI2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[159]"></a>MX_TIM14_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, tim.o(.text.MX_TIM14_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = MX_TIM14_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15a]"></a>MX_TIM3_Init</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, tim.o(.text.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15d]"></a>MX_TIM6_Init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, tim.o(.text.MX_TIM6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = MX_TIM6_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15e]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, usart.o(.text.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[34]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[15f]"></a>OLED_Clear</STRONG> (Thumb, 540 bytes, Stack size 56 bytes, oled.o(.text.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = OLED_Clear &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[160]"></a>OLED_Init</STRONG> (Thumb, 706 bytes, Stack size 48 bytes, oled.o(.text.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[161]"></a>OLED_ShowChar</STRONG> (Thumb, 932 bytes, Stack size 72 bytes, oled.o(.text.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = OLED_ShowChar &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
</UL>

<P><STRONG><a name="[162]"></a>OLED_ShowStr</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, oled.o(.text.OLED_ShowStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = OLED_ShowStr &rArr; OLED_ShowChar &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
</UL>

<P><STRONG><a name="[39]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[125]"></a>SDIO_ConfigData</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_ConfigData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_ConfigData
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[11f]"></a>SDIO_GetPowerState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_GetPowerState))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[113]"></a>SDIO_GetResponse</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_GetResponse))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[116]"></a>SDIO_Init</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[11b]"></a>SDIO_PowerState_ON</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_PowerState_ON))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[128]"></a>SDIO_ReadFIFO</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_ReadFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[12c]"></a>SDIO_WriteFIFO</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_WriteFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[115]"></a>SDMMC_CmdAppCommand</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdAppCommand))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[11e]"></a>SDMMC_CmdAppOperCommand</STRONG> (Thumb, 144 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdAppOperCommand))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[112]"></a>SDMMC_CmdBlockLength</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdBlockLength))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[117]"></a>SDMMC_CmdBusWidth</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdBusWidth))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[11c]"></a>SDMMC_CmdGoIdleState</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdGoIdleState))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[11d]"></a>SDMMC_CmdOperCond</STRONG> (Thumb, 166 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdOperCond))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[126]"></a>SDMMC_CmdReadMultiBlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdReadMultiBlock))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[127]"></a>SDMMC_CmdReadSingleBlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdReadSingleBlock))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[124]"></a>SDMMC_CmdSelDesel</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSelDesel))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[121]"></a>SDMMC_CmdSendCID</STRONG> (Thumb, 156 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSendCID))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[123]"></a>SDMMC_CmdSendCSD</STRONG> (Thumb, 156 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSendCSD))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[164]"></a>SDMMC_CmdSendSCR</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSendSCR))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[118]"></a>SDMMC_CmdSendStatus</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSendStatus))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>

<P><STRONG><a name="[122]"></a>SDMMC_CmdSetRelAdd</STRONG> (Thumb, 200 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSetRelAdd))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[129]"></a>SDMMC_CmdStopTransfer</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdStopTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[12a]"></a>SDMMC_CmdWriteMultiBlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdWriteMultiBlock))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[12b]"></a>SDMMC_CmdWriteSingleBlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdWriteSingleBlock))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[163]"></a>SDMMC_GetCmdResp1</STRONG> (Thumb, 372 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_GetCmdResp1))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
</UL>

<P><STRONG><a name="[ae]"></a>SD_initialize</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, sd_diskio.o(.text.SD_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = SD_initialize &rArr; BSP_SD_Init &rArr; HAL_SD_ConfigWideBusOperation &rArr; SD_FindSCR &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[b2]"></a>SD_ioctl</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, sd_diskio.o(.text.SD_ioctl))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SD_ioctl &rArr; BSP_SD_GetCardInfo &rArr; HAL_SD_GetCardInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardInfo
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[b0]"></a>SD_read</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sd_diskio.o(.text.SD_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SD_read &rArr; BSP_SD_ReadBlocks &rArr; HAL_SD_ReadBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_ReadBlocks
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[af]"></a>SD_status</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sd_diskio.o(.text.SD_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SD_status &rArr; BSP_SD_GetCardState &rArr; HAL_SD_GetCardState
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[b1]"></a>SD_write</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sd_diskio.o(.text.SD_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = SD_write &rArr; BSP_SD_WriteBlocks &rArr; HAL_SD_WriteBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_WriteBlocks
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[37]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[166]"></a>SystemClock_Config</STRONG> (Thumb, 176 bytes, Stack size 96 bytes, main.o(.text.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>SystemInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, system_stm32f4xx.o(.text.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[68]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.TIM8_TRG_COM_TIM14_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM8_TRG_COM_TIM14_IRQHandler &rArr; HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[133]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 324 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[60]"></a>USART1_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[177]"></a>WouoUI_Animation</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, wououi_anim.o(.text.WouoUI_Animation))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_Animation
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIn
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIn
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIn
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIn
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageShow
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageIn
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageShow
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIn
</UL>

<P><STRONG><a name="[181]"></a>WouoUI_BuffAllBlur</STRONG> (Thumb, 758 bytes, Stack size 20 bytes, wououi_graph.o(.text.WouoUI_BuffAllBlur))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = WouoUI_BuffAllBlur
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageShow
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageShow
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageShow
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageShow
</UL>

<P><STRONG><a name="[169]"></a>WouoUI_CanvasDrawASCII</STRONG> (Thumb, 2196 bytes, Stack size 40 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawASCII))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStrAutoNewline
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
</UL>

<P><STRONG><a name="[16b]"></a>WouoUI_CanvasDrawBMP</STRONG> (Thumb, 362 bytes, Stack size 48 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawBMP))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = WouoUI_CanvasDrawBMP &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageShow
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIn
</UL>

<P><STRONG><a name="[16c]"></a>WouoUI_CanvasDrawBoxRightAngle</STRONG> (Thumb, 558 bytes, Stack size 56 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawBoxRightAngle))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = WouoUI_CanvasDrawBoxRightAngle &rArr; WouoUI_CanvasDrawLine_V &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIndicatorCtrl
</UL>

<P><STRONG><a name="[16e]"></a>WouoUI_CanvasDrawLine_H</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawLine_H))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = WouoUI_CanvasDrawLine_H &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageScrollBarCtrl
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageScrollBarCtrl
</UL>

<P><STRONG><a name="[16d]"></a>WouoUI_CanvasDrawLine_V</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawLine_V))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_CanvasDrawLine_V &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBoxRightAngle
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageScrollBarCtrl
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageScrollBarCtrl
</UL>

<P><STRONG><a name="[16f]"></a>WouoUI_CanvasDrawPoint</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = WouoUI_CanvasDrawPoint &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
</UL>

<P><STRONG><a name="[170]"></a>WouoUI_CanvasDrawRBox</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawRBox))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxCommon
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageScrollBarCtrl
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIndicatorCtrl
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIn
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIndicatorCtrl
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageShow
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIn
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIndicatorCtrl
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageShow
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIn
<LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIndicatorCtrl
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageShow
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIn
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageShow
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageIn
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageScrollBarCtrl
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageIndicatorCtrl
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageShow
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIn
</UL>

<P><STRONG><a name="[171]"></a>WouoUI_CanvasDrawRBoxCommon</STRONG> (Thumb, 354 bytes, Stack size 64 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawRBoxCommon))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
</UL>

<P><STRONG><a name="[172]"></a>WouoUI_CanvasDrawRBoxEmpty</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawRBoxEmpty))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = WouoUI_CanvasDrawRBoxEmpty &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxCommon
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageIndicatorCtrl
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageIndicatorCtrl
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
</UL>

<P><STRONG><a name="[173]"></a>WouoUI_CanvasDrawSlideStr</STRONG> (Thumb, 288 bytes, Stack size 56 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawSlideStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawASCII
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListAuotCanvasDrawLineTailValTxt
</UL>

<P><STRONG><a name="[175]"></a>WouoUI_CanvasDrawStr</STRONG> (Thumb, 80 bytes, Stack size 48 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = WouoUI_CanvasDrawStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawASCII
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListAuotCanvasDrawLineTailValTxt
</UL>

<P><STRONG><a name="[176]"></a>WouoUI_CanvasDrawStrAutoNewline</STRONG> (Thumb, 122 bytes, Stack size 56 bytes, wououi_graph.o(.text.WouoUI_CanvasDrawStrAutoNewline))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = WouoUI_CanvasDrawStrAutoNewline &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawASCII
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageShow
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageIn
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
</UL>

<P><STRONG><a name="[17d]"></a>WouoUI_CanvasSlideStrReset</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, wououi_graph.o(.text.WouoUI_CanvasSlideStrReset))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageInParaInit
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageInParaInit
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageInParaInit
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageInParaInit
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageInParaInit
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageInParaInit
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageReact
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageInParaInit
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageToggleBtn
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageNextItem
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageLastItem
</UL>

<P><STRONG><a name="[16]"></a>WouoUI_ConfWinPageIn</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_ConfWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = WouoUI_ConfWinPageIn &rArr; _WouoUI_ConfWinPageDraw &rArr; WouoUI_CanvasDrawStrAutoNewline &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[17]"></a>WouoUI_ConfWinPageInParaInit</STRONG> (Thumb, 402 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_ConfWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_ConfWinPageInParaInit &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrHeightAutoNewLine
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[1a]"></a>WouoUI_ConfWinPageIndicatorCtrl</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, wououi_win.o(.text.WouoUI_ConfWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = WouoUI_ConfWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[19]"></a>WouoUI_ConfWinPageReact</STRONG> (Thumb, 276 bytes, Stack size 16 bytes, wououi_win.o(.text.WouoUI_ConfWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_ConfWinPageReact &rArr; WouoUI_ConfWinPageToggleBtn &rArr; WouoUI_GetStrWidth
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageToggleBtn
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[18]"></a>WouoUI_ConfWinPageShow</STRONG> (Thumb, 150 bytes, Stack size 24 bytes, wououi_win.o(.text.WouoUI_ConfWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = WouoUI_ConfWinPageShow &rArr; _WouoUI_ConfWinPageDraw &rArr; WouoUI_CanvasDrawStrAutoNewline &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[17f]"></a>WouoUI_ConfWinPageToggleBtn</STRONG> (Thumb, 164 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_ConfWinPageToggleBtn))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = WouoUI_ConfWinPageToggleBtn &rArr; WouoUI_GetStrWidth
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
</UL>

<P><STRONG><a name="[5]"></a>WouoUI_FuncDoNothing</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, wououi.o(.text.WouoUI_FuncDoNothing))
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[c]"></a>WouoUI_FuncDoNothingRetTrue</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, wououi.o(.text.WouoUI_FuncDoNothingRetTrue))
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[17b]"></a>WouoUI_GetStrHeightAutoNewLine</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, wououi_graph.o(.text.WouoUI_GetStrHeightAutoNewLine))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_GetStrHeightAutoNewLine
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageInParaInit
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageInParaInit
</UL>

<P><STRONG><a name="[17c]"></a>WouoUI_GetStrWidth</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, wououi_graph.o(.text.WouoUI_GetStrWidth))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WouoUI_GetStrWidth
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIndicatorCtrl
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageInParaInit
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageToggleBtn
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ConfWinPageDraw
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListAuotCanvasDrawLineTailValTxt
</UL>

<P><STRONG><a name="[178]"></a>WouoUI_GraphSetPenColor</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, wououi_graph.o(.text.WouoUI_GraphSetPenColor))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIndicatorCtrl
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIn
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIndicatorCtrl
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageShow
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIn
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIndicatorCtrl
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageShow
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIn
<LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIndicatorCtrl
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageShow
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIn
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageShow
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageIn
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageIndicatorCtrl
<LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageIndicatorCtrl
</UL>

<P><STRONG><a name="[6]"></a>WouoUI_ListPageIn</STRONG> (Thumb, 186 bytes, Stack size 32 bytes, wououi_page.o(.text.WouoUI_ListPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = WouoUI_ListPageIn &rArr; WouoUI_ListDrawText_CheckBox &rArr; WouoUI_ListAuotCanvasDrawLineTailValTxt &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[7]"></a>WouoUI_ListPageInParaInit</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, wououi_page.o(.text.WouoUI_ListPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WouoUI_ListPageInParaInit
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[a]"></a>WouoUI_ListPageIndicatorCtrl</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, wououi_page.o(.text.WouoUI_ListPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = WouoUI_ListPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[187]"></a>WouoUI_ListPageLastItem</STRONG> (Thumb, 216 bytes, Stack size 16 bytes, wououi_page.o(.text.WouoUI_ListPageLastItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_ListPageLastItem
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageReact
</UL>

<P><STRONG><a name="[188]"></a>WouoUI_ListPageNextItem</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, wououi_page.o(.text.WouoUI_ListPageNextItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_ListPageNextItem
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageReact
</UL>

<P><STRONG><a name="[9]"></a>WouoUI_ListPageReact</STRONG> (Thumb, 338 bytes, Stack size 32 bytes, wououi_page.o(.text.WouoUI_ListPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = WouoUI_ListPageReact &rArr; WouoUI_ListPageNextItem
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memchr
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageNextItem
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageLastItem
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[b]"></a>WouoUI_ListPageScrollBarCtrl</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, wououi_page.o(.text.WouoUI_ListPageScrollBarCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = WouoUI_ListPageScrollBarCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_H
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[8]"></a>WouoUI_ListPageShow</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, wououi_page.o(.text.WouoUI_ListPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = WouoUI_ListPageShow &rArr; WouoUI_ListDrawText_CheckBox &rArr; WouoUI_ListAuotCanvasDrawLineTailValTxt &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[25]"></a>WouoUI_ListWinPageIn</STRONG> (Thumb, 104 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_ListWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = WouoUI_ListWinPageIn &rArr; _WouoUI_listWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[26]"></a>WouoUI_ListWinPageInParaInit</STRONG> (Thumb, 170 bytes, Stack size 8 bytes, wououi_win.o(.text.WouoUI_ListWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = WouoUI_ListWinPageInParaInit &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[29]"></a>WouoUI_ListWinPageIndicatorCtrl</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_ListWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = WouoUI_ListWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[18a]"></a>WouoUI_ListWinPageLastItem</STRONG> (Thumb, 226 bytes, Stack size 16 bytes, wououi_win.o(.text.WouoUI_ListWinPageLastItem))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_ListWinPageLastItem
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
</UL>

<P><STRONG><a name="[28]"></a>WouoUI_ListWinPageReact</STRONG> (Thumb, 442 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_ListWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = WouoUI_ListWinPageReact &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageLastItem
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[2a]"></a>WouoUI_ListWinPageScrollBarCtrl</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, wououi_win.o(.text.WouoUI_ListWinPageScrollBarCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = WouoUI_ListWinPageScrollBarCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_H
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[27]"></a>WouoUI_ListWinPageShow</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, wououi_win.o(.text.WouoUI_ListWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = WouoUI_ListWinPageShow &rArr; _WouoUI_listWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_listWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[17e]"></a>WouoUI_MsgQueRead</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, wououi_msg.o(.text.WouoUI_MsgQueRead))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageReact
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageReact
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageReact
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageReact
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_TitlePageReact
</UL>

<P><STRONG><a name="[202]"></a>WouoUI_MsgQueSend</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, wououi_msg.o(.text.WouoUI_MsgQueSend))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
</UL>

<P><STRONG><a name="[11]"></a>WouoUI_MsgWinPageIn</STRONG> (Thumb, 226 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_MsgWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = WouoUI_MsgWinPageIn &rArr; WouoUI_CanvasDrawStrAutoNewline &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStrAutoNewline
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[12]"></a>WouoUI_MsgWinPageInParaInit</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, wououi_win.o(.text.WouoUI_MsgWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = WouoUI_MsgWinPageInParaInit &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrHeightAutoNewLine
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[15]"></a>WouoUI_MsgWinPageIndicatorCtrl</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, wououi_win.o(.text.WouoUI_MsgWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = WouoUI_MsgWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBoxEmpty &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[14]"></a>WouoUI_MsgWinPageReact</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, wououi_win.o(.text.WouoUI_MsgWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_MsgWinPageReact
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[13]"></a>WouoUI_MsgWinPageShow</STRONG> (Thumb, 166 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_MsgWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = WouoUI_MsgWinPageShow &rArr; WouoUI_CanvasDrawStrAutoNewline &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStrAutoNewline
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[180]"></a>WouoUI_PageReturn</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, wououi_page.o(.text.WouoUI_PageReturn))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageReact
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageReact
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageReact
</UL>

<P><STRONG><a name="[20]"></a>WouoUI_SpinWinPageIn</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_SpinWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = WouoUI_SpinWinPageIn &rArr; _WouoUI_SpinWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[21]"></a>WouoUI_SpinWinPageInParaInit</STRONG> (Thumb, 432 bytes, Stack size 16 bytes, wououi_win.o(.text.WouoUI_SpinWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = WouoUI_SpinWinPageInParaInit &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[24]"></a>WouoUI_SpinWinPageIndicatorCtrl</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_SpinWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = WouoUI_SpinWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[23]"></a>WouoUI_SpinWinPageReact</STRONG> (Thumb, 734 bytes, Stack size 16 bytes, wououi_win.o(.text.WouoUI_SpinWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = WouoUI_SpinWinPageReact &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[22]"></a>WouoUI_SpinWinPageShow</STRONG> (Thumb, 198 bytes, Stack size 24 bytes, wououi_win.o(.text.WouoUI_SpinWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = WouoUI_SpinWinPageShow &rArr; _WouoUI_SpinWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[0]"></a>WouoUI_TitlePageIn</STRONG> (Thumb, 298 bytes, Stack size 48 bytes, wououi_page.o(.text.WouoUI_TitlePageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = WouoUI_TitlePageIn &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBMP
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[1]"></a>WouoUI_TitlePageInParaInit</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, wououi_page.o(.text.WouoUI_TitlePageInParaInit))
<BR><BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[4]"></a>WouoUI_TitlePageIndicatorCtrl</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, wououi_page.o(.text.WouoUI_TitlePageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = WouoUI_TitlePageIndicatorCtrl &rArr; WouoUI_CanvasDrawBoxRightAngle &rArr; WouoUI_CanvasDrawLine_V &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBoxRightAngle
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[3]"></a>WouoUI_TitlePageReact</STRONG> (Thumb, 382 bytes, Stack size 16 bytes, wououi_page.o(.text.WouoUI_TitlePageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = WouoUI_TitlePageReact
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[2]"></a>WouoUI_TitlePageShow</STRONG> (Thumb, 256 bytes, Stack size 40 bytes, wououi_page.o(.text.WouoUI_TitlePageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = WouoUI_TitlePageShow &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBMP
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[1b]"></a>WouoUI_ValWinPageIn</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, wououi_win.o(.text.WouoUI_ValWinPageIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = WouoUI_ValWinPageIn &rArr; _WouoUI_ValWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[1c]"></a>WouoUI_ValWinPageInParaInit</STRONG> (Thumb, 398 bytes, Stack size 16 bytes, wououi_win.o(.text.WouoUI_ValWinPageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = WouoUI_ValWinPageInParaInit &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[1f]"></a>WouoUI_ValWinPageIndicatorCtrl</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, wououi_win.o(.text.WouoUI_ValWinPageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = WouoUI_ValWinPageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBox &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[1e]"></a>WouoUI_ValWinPageReact</STRONG> (Thumb, 266 bytes, Stack size 16 bytes, wououi_win.o(.text.WouoUI_ValWinPageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = WouoUI_ValWinPageReact &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_PageReturn
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[1d]"></a>WouoUI_ValWinPageShow</STRONG> (Thumb, 222 bytes, Stack size 24 bytes, wououi_win.o(.text.WouoUI_ValWinPageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = WouoUI_ValWinPageShow &rArr; _WouoUI_ValWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_BuffAllBlur
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GraphSetPenColor
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[d]"></a>WouoUI_WavePageInParaInit</STRONG> (Thumb, 238 bytes, Stack size 8 bytes, wououi_page.o(.text.WouoUI_WavePageInParaInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = WouoUI_WavePageInParaInit
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[10]"></a>WouoUI_WavePageIndicatorCtrl</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, wououi_page.o(.text.WouoUI_WavePageIndicatorCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = WouoUI_WavePageIndicatorCtrl &rArr; WouoUI_CanvasDrawRBoxEmpty &rArr; WouoUI_CanvasDrawRBoxCommon &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[f]"></a>WouoUI_WavePageReact</STRONG> (Thumb, 658 bytes, Stack size 16 bytes, wououi_page.o(.text.WouoUI_WavePageReact))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = WouoUI_WavePageReact &rArr; _WouoUI_WaveUpdateRange
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueRead
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasSlideStrReset
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_WaveUpdateRange
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[e]"></a>WouoUI_WavePageShow</STRONG> (Thumb, 1074 bytes, Stack size 48 bytes, wououi_page.o(.text.WouoUI_WavePageShow))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = WouoUI_WavePageShow &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawPoint
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_Animation
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
</UL>
<BR>[Address Reference Count : 1]<UL><LI> wououi.o(.data.default_ui)
</UL>
<P><STRONG><a name="[191]"></a>__aeabi_assert</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, assert_override.o(.text.__aeabi_assert))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_init
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
</UL>

<P><STRONG><a name="[2c]"></a>adc_task</STRONG> (Thumb, 436 bytes, Stack size 56 bytes, adc_app.o(.text.adc_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = adc_task &rArr; HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[192]"></a>adc_tim_dma_init</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, adc_app.o(.text.adc_tim_dma_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = adc_tim_dma_init &rArr; HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[194]"></a>app_btn_init</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, btn_app.o(.text.app_btn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = app_btn_init &rArr; ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2d]"></a>btn_task</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, btn_app.o(.text.btn_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = btn_task &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[198]"></a>check_flash_status</STRONG> (Thumb, 310 bytes, Stack size 24 bytes, system_check.o(.text.check_flash_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = check_flash_status &rArr; spi_flash_read_id &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
</UL>

<P><STRONG><a name="[19d]"></a>config_calculate_crc32</STRONG> (Thumb, 742 bytes, Stack size 8 bytes, config_manager.o(.text.config_calculate_crc32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = config_calculate_crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_init
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_sampling_cycle
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_load_from_flash
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_params
</UL>

<P><STRONG><a name="[1db]"></a>config_get_params</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, config_manager.o(.text.config_get_params))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = config_get_params
</UL>
<BR>[Called By]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_check_overlimit
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_voltage
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configread_command
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configsave_command
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_command
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_command
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
</UL>

<P><STRONG><a name="[208]"></a>config_get_sampling_cycle</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, config_manager.o(.text.config_get_sampling_cycle))
<BR><BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_init
</UL>

<P><STRONG><a name="[19b]"></a>config_init</STRONG> (Thumb, 204 bytes, Stack size 48 bytes, config_manager.o(.text.config_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = config_init &rArr; spi_flash_buffer_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[19e]"></a>config_load_from_flash</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, config_manager.o(.text.config_load_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = config_load_from_flash &rArr; spi_flash_buffer_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configread_command
</UL>

<P><STRONG><a name="[19f]"></a>config_save_to_flash</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, config_manager.o(.text.config_save_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = config_save_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_set_cycle
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configsave_command
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
</UL>

<P><STRONG><a name="[1a2]"></a>config_set_params</STRONG> (Thumb, 132 bytes, Stack size 8 bytes, config_manager.o(.text.config_set_params))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = config_set_params &rArr; config_calculate_crc32
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
</UL>

<P><STRONG><a name="[1a3]"></a>config_set_sampling_cycle</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, config_manager.o(.text.config_set_sampling_cycle))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = config_set_sampling_cycle &rArr; config_calculate_crc32
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_set_cycle
</UL>

<P><STRONG><a name="[1da]"></a>config_validate_limit</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, config_manager.o(.text.config_validate_limit))
<BR><BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
</UL>

<P><STRONG><a name="[1d9]"></a>config_validate_ratio</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, config_manager.o(.text.config_validate_ratio))
<BR><BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
</UL>

<P><STRONG><a name="[1b3]"></a>convert_rtc_to_unix_timestamp</STRONG> (Thumb, 578 bytes, Stack size 64 bytes, usart_app.o(.text.convert_rtc_to_unix_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = convert_rtc_to_unix_timestamp
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
</UL>

<P><STRONG><a name="[1a7]"></a>data_storage_init</STRONG> (Thumb, 408 bytes, Stack size 592 bytes, data_storage.o(.text.data_storage_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 1168<LI>Call Chain = data_storage_init &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1af]"></a>data_storage_write_hidedata</STRONG> (Thumb, 192 bytes, Stack size 512 bytes, data_storage.o(.text.data_storage_write_hidedata))
<BR><BR>[Stack]<UL><LI>Max Depth = 1560<LI>Call Chain = data_storage_write_hidedata &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;format_hex_output
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_rtc_to_unix_timestamp
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
</UL>

<P><STRONG><a name="[1b6]"></a>data_storage_write_log</STRONG> (Thumb, 116 bytes, Stack size 320 bytes, data_storage.o(.text.data_storage_write_log))
<BR><BR>[Stack]<UL><LI>Max Depth = 1368<LI>Call Chain = data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_command
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_command
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_command
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_command
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1b7]"></a>data_storage_write_overlimit</STRONG> (Thumb, 168 bytes, Stack size 232 bytes, data_storage.o(.text.data_storage_write_overlimit))
<BR><BR>[Stack]<UL><LI>Max Depth = 1280<LI>Call Chain = data_storage_write_overlimit &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
</UL>

<P><STRONG><a name="[1b8]"></a>data_storage_write_sample</STRONG> (Thumb, 152 bytes, Stack size 216 bytes, data_storage.o(.text.data_storage_write_sample))
<BR><BR>[Stack]<UL><LI>Max Depth = 1264<LI>Call Chain = data_storage_write_sample &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
</UL>

<P><STRONG><a name="[1b9]"></a>device_id_init</STRONG> (Thumb, 222 bytes, Stack size 104 bytes, device_id.o(.text.device_id_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = device_id_init &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1bc]"></a>device_id_print_startup_info</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, device_id.o(.text.device_id_print_startup_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = device_id_print_startup_info &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1d4]"></a>disk_initialize</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, diskio.o(.text.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = disk_initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
</UL>

<P><STRONG><a name="[20a]"></a>disk_ioctl</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, diskio.o(.text.disk_ioctl))
<BR><BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
</UL>

<P><STRONG><a name="[1ca]"></a>disk_read</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, diskio.o(.text.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[1c7]"></a>disk_status</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, diskio.o(.text.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[1cb]"></a>disk_write</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, diskio.o(.text.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[195]"></a>ebtn_init</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, ebtn.o(.text.ebtn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
</UL>

<P><STRONG><a name="[197]"></a>ebtn_process</STRONG> (Thumb, 154 bytes, Stack size 40 bytes, ebtn.o(.text.ebtn_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = ebtn_process &rArr; ebtn_process_with_curr_state &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btn_task
</UL>

<P><STRONG><a name="[1c4]"></a>ebtn_process_with_curr_state</STRONG> (Thumb, 728 bytes, Stack size 64 bytes, ebtn.o(.text.ebtn_process_with_curr_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = ebtn_process_with_curr_state &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[1ad]"></a>f_close</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, ff.o(.text.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = f_close &rArr; f_sync &rArr; sync_fs &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_file
</UL>

<P><STRONG><a name="[1c8]"></a>f_gets</STRONG> (Thumb, 104 bytes, Stack size 40 bytes, ff.o(.text.f_gets))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = f_gets &rArr; f_read &rArr; get_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_file
</UL>

<P><STRONG><a name="[1c9]"></a>f_lseek</STRONG> (Thumb, 668 bytes, Stack size 40 bytes, ff.o(.text.f_lseek))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = f_lseek &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
</UL>

<P><STRONG><a name="[1aa]"></a>f_mkdir</STRONG> (Thumb, 696 bytes, Stack size 104 bytes, ff.o(.text.f_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = f_mkdir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
</UL>

<P><STRONG><a name="[1a9]"></a>f_mount</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, ff.o(.text.f_mount))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = f_mount &rArr; find_volume &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
</UL>

<P><STRONG><a name="[1ab]"></a>f_open</STRONG> (Thumb, 1006 bytes, Stack size 104 bytes, ff.o(.text.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_file
</UL>

<P><STRONG><a name="[1ac]"></a>f_read</STRONG> (Thumb, 574 bytes, Stack size 40 bytes, ff.o(.text.f_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = f_read &rArr; get_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
</UL>

<P><STRONG><a name="[1c6]"></a>f_sync</STRONG> (Thumb, 198 bytes, Stack size 16 bytes, ff.o(.text.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = f_sync &rArr; sync_fs &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
</UL>

<P><STRONG><a name="[1ae]"></a>f_write</STRONG> (Thumb, 566 bytes, Stack size 40 bytes, ff.o(.text.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
</UL>

<P><STRONG><a name="[1d5]"></a>ff_convert</STRONG> (Thumb, 148 bytes, Stack size 20 bytes, cc936.o(.text.ff_convert))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ff_convert
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[1cd]"></a>ff_memalloc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, syscall.o(.text.ff_memalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ff_memalloc &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1d1]"></a>ff_memfree</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, syscall.o(.text.ff_memfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ff_memfree &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1c1]"></a>ff_wtoupper</STRONG> (Thumb, 192 bytes, Stack size 8 bytes, cc936.o(.text.ff_wtoupper))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[1b4]"></a>format_hex_output</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, usart_app.o(.text.format_hex_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = format_hex_output &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
</UL>

<P><STRONG><a name="[ab]"></a>fputc</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usart.o(.text.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = fputc &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[1d6]"></a>generate_filename</STRONG> (Thumb, 176 bytes, Stack size 80 bytes, data_storage.o(.text.generate_filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = generate_filename &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
</UL>

<P><STRONG><a name="[1cf]"></a>get_fattime</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fatfs.o(.text.get_fattime))
<BR><BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1d7]"></a>handle_conf_command</STRONG> (Thumb, 392 bytes, Stack size 192 bytes, usart_app.o(.text.handle_conf_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1560<LI>Call Chain = handle_conf_command &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_params
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_validate_limit
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_validate_ratio
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_file
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1dc]"></a>handle_configread_command</STRONG> (Thumb, 140 bytes, Stack size 32 bytes, usart_app.o(.text.handle_configread_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = handle_configread_command &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_load_from_flash
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1dd]"></a>handle_configsave_command</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, usart_app.o(.text.handle_configsave_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = handle_configsave_command &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1de]"></a>handle_interactive_input</STRONG> (Thumb, 604 bytes, Stack size 224 bytes, usart_app.o(.text.handle_interactive_input))
<BR><BR>[Stack]<UL><LI>Max Depth = 1592<LI>Call Chain = handle_interactive_input &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_params
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_validate_limit
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_validate_ratio
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1e0]"></a>handle_limit_command</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, usart_app.o(.text.handle_limit_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1400<LI>Call Chain = handle_limit_command &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1e1]"></a>handle_ratio_command</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, usart_app.o(.text.handle_ratio_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1400<LI>Call Chain = handle_ratio_command &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1e2]"></a>handle_sampling_output</STRONG> (Thumb, 1132 bytes, Stack size 176 bytes, usart_app.o(.text.handle_sampling_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 1736<LI>Call Chain = handle_sampling_output &rArr; data_storage_write_hidedata &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_check_overlimit
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_voltage
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_state
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_cycle
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_overlimit
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_sample
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[1e7]"></a>handle_start_command</STRONG> (Thumb, 108 bytes, Stack size 80 bytes, usart_app.o(.text.handle_start_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1448<LI>Call Chain = handle_start_command &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_init
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_cycle
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_start
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1ea]"></a>handle_stop_command</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, usart_app.o(.text.handle_stop_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1376<LI>Call Chain = handle_stop_command &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_init
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1d8]"></a>ini_parse_file</STRONG> (Thumb, 104 bytes, Stack size 704 bytes, ini_parser.o(.text.ini_parse_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 1080<LI>Call Chain = ini_parse_file &rArr; ini_parse_line &rArr; __hardfp_strtof &rArr; __strtof_int &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_line
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
</UL>

<P><STRONG><a name="[1ec]"></a>ini_parse_line</STRONG> (Thumb, 948 bytes, Stack size 160 bytes, ini_parser.o(.text.ini_parse_line))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = ini_parse_line &rArr; __hardfp_strtof &rArr; __strtof_int &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memmove
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_strtof
</UL>
<BR>[Called By]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_file
</UL>

<P><STRONG><a name="[1f0]"></a>led_disp</STRONG> (Thumb, 200 bytes, Stack size 40 bytes, led_app.o(.text.led_disp))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_task
</UL>

<P><STRONG><a name="[2b]"></a>led_task</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, led_app.o(.text.led_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = led_task &rArr; sampling_check_overlimit &rArr; config_get_params
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_check_overlimit
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_state
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[95]"></a>main</STRONG> (Thumb, 348 bytes, Stack size 0 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1368<LI>Call Chain = main &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_print_startup_info
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_init
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_init
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_SetPowerSave
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_InitDisplay
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x32_univision_f
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_tim_dma_init
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_init
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SDIO_SD_Init
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM14_Init
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[1a8]"></a>my_printf</STRONG> (Thumb, 62 bytes, Stack size 544 bytes, usart_app.o(.text.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_print_startup_info
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_system_info
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_print_current_time
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_command
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_command
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configread_command
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configsave_command
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_command
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_command
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_unhide_conversion
</UL>

<P><STRONG><a name="[1fa]"></a>oled_printf</STRONG> (Thumb, 58 bytes, Stack size 544 bytes, oled_app.o(.text.oled_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 744<LI>Call Chain = oled_printf &rArr; OLED_ShowStr &rArr; OLED_ShowChar &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[2f]"></a>oled_task</STRONG> (Thumb, 240 bytes, Stack size 56 bytes, oled_app.o(.text.oled_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 800<LI>Call Chain = oled_task &rArr; oled_printf &rArr; OLED_ShowStr &rArr; OLED_ShowChar &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_voltage
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_state
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[1fb]"></a>parse_uart_command</STRONG> (Thumb, 900 bytes, Stack size 72 bytes, usart_app.o(.text.parse_uart_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1664<LI>Call Chain = parse_uart_command &rArr; handle_interactive_input &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_command
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_command
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configread_command
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_configsave_command
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_command
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_command
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_overlimit
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_sample
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_unhide_conversion
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[200]"></a>print_system_info</STRONG> (Thumb, 168 bytes, Stack size 16 bytes, system_check.o(.text.print_system_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = print_system_info &rArr; rtc_print_current_time &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_print_current_time
</UL>
<BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_self_check
</UL>

<P><STRONG><a name="[a3]"></a>prv_btn_event</STRONG> (Thumb, 548 bytes, Stack size 80 bytes, btn_app.o(.text.prv_btn_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 1448<LI>Call Chain = prv_btn_event &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_init
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgQueSend
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_set_cycle
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_state
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_stop
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_get_cycle
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_start
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(.text.app_btn_init)
</UL>
<P><STRONG><a name="[a4]"></a>prv_btn_get_state</STRONG> (Thumb, 170 bytes, Stack size 8 bytes, btn_app.o(.text.prv_btn_get_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prv_btn_get_state
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(.text.app_btn_init)
</UL>
<P><STRONG><a name="[220]"></a>rt_ringbuffer_data_len</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, ringbuffer.o(.text.rt_ringbuffer_data_len))
<BR><BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[206]"></a>rt_ringbuffer_get</STRONG> (Thumb, 210 bytes, Stack size 24 bytes, ringbuffer.o(.text.rt_ringbuffer_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = rt_ringbuffer_get &rArr; __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[1f2]"></a>rt_ringbuffer_init</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, ringbuffer.o(.text.rt_ringbuffer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rt_ringbuffer_init &rArr; __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13f]"></a>rt_ringbuffer_put</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, ringbuffer.o(.text.rt_ringbuffer_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = rt_ringbuffer_put &rArr; __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[201]"></a>rtc_print_current_time</STRONG> (Thumb, 104 bytes, Stack size 48 bytes, rtc_app.o(.text.rtc_print_current_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = rtc_print_current_time &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_system_info
</UL>

<P><STRONG><a name="[1df]"></a>rtc_set_time_from_string</STRONG> (Thumb, 264 bytes, Stack size 88 bytes, rtc_app.o(.text.rtc_set_time_from_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = rtc_set_time_from_string &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
</UL>

<P><STRONG><a name="[1e6]"></a>sampling_check_overlimit</STRONG> (Thumb, 84 bytes, Stack size 56 bytes, sampling_control.o(.text.sampling_check_overlimit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = sampling_check_overlimit &rArr; config_get_params
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_task
</UL>

<P><STRONG><a name="[1e4]"></a>sampling_get_cycle</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, sampling_control.o(.text.sampling_get_cycle))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_command
</UL>

<P><STRONG><a name="[1e3]"></a>sampling_get_state</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sampling_control.o(.text.sampling_get_state))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_task
</UL>

<P><STRONG><a name="[1e5]"></a>sampling_get_voltage</STRONG> (Thumb, 46 bytes, Stack size 32 bytes, sampling_control.o(.text.sampling_get_voltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sampling_get_voltage &rArr; config_get_params
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[1e8]"></a>sampling_init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, sampling_control.o(.text.sampling_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = sampling_init &rArr; config_init &rArr; spi_flash_buffer_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_init
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_sampling_cycle
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_command
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_command
</UL>

<P><STRONG><a name="[203]"></a>sampling_set_cycle</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, sampling_control.o(.text.sampling_set_cycle))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = sampling_set_cycle &rArr; config_save_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_sampling_cycle
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
</UL>

<P><STRONG><a name="[1e9]"></a>sampling_start</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, sampling_control.o(.text.sampling_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sampling_start
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_command
</UL>

<P><STRONG><a name="[1eb]"></a>sampling_stop</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, sampling_control.o(.text.sampling_stop))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_command
</UL>

<P><STRONG><a name="[30]"></a>sampling_task</STRONG> (Thumb, 272 bytes, Stack size 88 bytes, sampling_control.o(.text.sampling_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 1648<LI>Call Chain = sampling_task &rArr; data_storage_write_hidedata &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_params
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_overlimit
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_sample
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[1f7]"></a>scheduler_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scheduler.o(.text.scheduler_init))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f8]"></a>scheduler_run</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, scheduler.o(.text.scheduler_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = scheduler_run
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[19c]"></a>spi_flash_buffer_read</STRONG> (Thumb, 220 bytes, Stack size 56 bytes, gd25qxx.o(.text.spi_flash_buffer_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = spi_flash_buffer_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_init
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_load_from_flash
</UL>

<P><STRONG><a name="[1a1]"></a>spi_flash_buffer_write</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd25qxx.o(.text.spi_flash_buffer_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
</UL>

<P><STRONG><a name="[1f6]"></a>spi_flash_init</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd25qxx.o(.text.spi_flash_init))
<BR><BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[209]"></a>spi_flash_page_write</STRONG> (Thumb, 362 bytes, Stack size 40 bytes, gd25qxx.o(.text.spi_flash_page_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>

<P><STRONG><a name="[199]"></a>spi_flash_read_id</STRONG> (Thumb, 172 bytes, Stack size 40 bytes, gd25qxx.o(.text.spi_flash_read_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = spi_flash_read_id &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_flash_status
</UL>

<P><STRONG><a name="[1a0]"></a>spi_flash_sector_erase</STRONG> (Thumb, 294 bytes, Stack size 48 bytes, gd25qxx.o(.text.spi_flash_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = spi_flash_sector_erase &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
</UL>

<P><STRONG><a name="[1fe]"></a>system_self_check</STRONG> (Thumb, 176 bytes, Stack size 64 bytes, system_check.o(.text.system_self_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 1432<LI>Call Chain = system_self_check &rArr; data_storage_write_log &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_system_info
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_flash_status
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[210]"></a>u8g2_IsIntersection</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, u8g2_intersection.o(.text.u8g2_IsIntersection))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8g2_IsIntersection
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_update_page_win_r0
</UL>

<P><STRONG><a name="[20c]"></a>u8g2_SetFontPosBaseline</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8g2_font.o(.text.u8g2_SetFontPosBaseline))
<BR><BR>[Called By]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
</UL>

<P><STRONG><a name="[20b]"></a>u8g2_SetupBuffer</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, u8g2_setup.o(.text.u8g2_SetupBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_SetupBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetFontPosBaseline
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x32_univision_f
</UL>

<P><STRONG><a name="[1f3]"></a>u8g2_Setup_ssd1306_i2c_128x32_univision_f</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, u8g2_d_setup.o(.text.u8g2_Setup_ssd1306_i2c_128x32_univision_f))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8g2_Setup_ssd1306_i2c_128x32_univision_f &rArr; u8g2_SetupBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_SetupBuffer
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_Setup
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_m_16_4_f
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[20f]"></a>u8g2_draw_hv_line_2dir</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, u8g2_hvline.o(.text.u8g2_draw_hv_line_2dir))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_draw_hv_line_2dir
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_l90_r0
</UL>

<P><STRONG><a name="[b5]"></a>u8g2_draw_l90_r0</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, u8g2_setup.o(.text.u8g2_draw_l90_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_draw_l90_r0 &rArr; u8g2_draw_hv_line_2dir
</UL>
<BR>[Calls]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_draw_hv_line_2dir
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.rodata.u8g2_cb_r0)
</UL>
<P><STRONG><a name="[a7]"></a>u8g2_font_calc_vref_font</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, u8g2_font.o(.text.u8g2_font_calc_vref_font))
<BR>[Address Reference Count : 1]<UL><LI> u8g2_font.o(.text.u8g2_SetFontPosBaseline)
</UL>
<P><STRONG><a name="[a6]"></a>u8g2_gpio_and_delay_stm32</STRONG> (Thumb, 222 bytes, Stack size 16 bytes, oled_app.o(.text.u8g2_gpio_and_delay_stm32))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_gpio_and_delay_stm32 &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(.text.main)
</UL>
<P><STRONG><a name="[aa]"></a>u8g2_ll_hvline_vertical_top_lsb</STRONG> (Thumb, 638 bytes, Stack size 24 bytes, u8g2_ll_hvline.o(.text.u8g2_ll_hvline_vertical_top_lsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8g2_ll_hvline_vertical_top_lsb
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(.text.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
</UL>
<P><STRONG><a name="[20e]"></a>u8g2_m_16_4_f</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, u8g2_d_memory.o(.text.u8g2_m_16_4_f))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x32_univision_f
</UL>

<P><STRONG><a name="[b3]"></a>u8g2_update_dimension_r0</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, u8g2_setup.o(.text.u8g2_update_dimension_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8g2_update_dimension_r0
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.rodata.u8g2_cb_r0)
</UL>
<P><STRONG><a name="[b4]"></a>u8g2_update_page_win_r0</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, u8g2_setup.o(.text.u8g2_update_page_win_r0))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = u8g2_update_page_win_r0 &rArr; u8g2_IsIntersection
</UL>
<BR>[Calls]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_IsIntersection
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_setup.o(.rodata.u8g2_cb_r0)
</UL>
<P><STRONG><a name="[1f4]"></a>u8x8_InitDisplay</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_display.o(.text.u8x8_InitDisplay))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f5]"></a>u8x8_SetPowerSave</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_display.o(.text.u8x8_SetPowerSave))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[20d]"></a>u8x8_Setup</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, u8x8_setup.o(.text.u8x8_Setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_Setup
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8g2_Setup_ssd1306_i2c_128x32_univision_f
</UL>

<P><STRONG><a name="[241]"></a>u8x8_SetupMemory</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_display.o(.text.u8x8_SetupMemory), UNUSED)

<P><STRONG><a name="[216]"></a>u8x8_byte_EndTransfer</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_byte.o(.text.u8x8_byte_EndTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
</UL>

<P><STRONG><a name="[211]"></a>u8x8_byte_SendByte</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, u8x8_byte.o(.text.u8x8_byte_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = u8x8_byte_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
</UL>

<P><STRONG><a name="[212]"></a>u8x8_byte_SendBytes</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_byte.o(.text.u8x8_byte_SendBytes))
<BR><BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>

<P><STRONG><a name="[218]"></a>u8x8_byte_StartTransfer</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_byte.o(.text.u8x8_byte_StartTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
</UL>

<P><STRONG><a name="[a5]"></a>u8x8_byte_hw_i2c</STRONG> (Thumb, 256 bytes, Stack size 24 bytes, oled_app.o(.text.u8x8_byte_hw_i2c))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = u8x8_byte_hw_i2c &rArr; HAL_I2C_Master_Transmit &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(.text.main)
</UL>
<P><STRONG><a name="[21e]"></a>u8x8_cad_EndTransfer</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_cad.o(.text.u8x8_cad_EndTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[21d]"></a>u8x8_cad_SendArg</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_cad.o(.text.u8x8_cad_SendArg))
<BR><BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[21c]"></a>u8x8_cad_SendCmd</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_cad.o(.text.u8x8_cad_SendCmd))
<BR><BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[215]"></a>u8x8_cad_SendData</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_cad.o(.text.u8x8_cad_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[213]"></a>u8x8_cad_SendSequence</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, u8x8_cad.o(.text.u8x8_cad_SendSequence))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = u8x8_cad_SendSequence
</UL>
<BR>[Calls]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendData
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[21b]"></a>u8x8_cad_StartTransfer</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, u8x8_cad.o(.text.u8x8_cad_StartTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[a9]"></a>u8x8_cad_ssd13xx_fast_i2c</STRONG> (Thumb, 526 bytes, Stack size 24 bytes, u8x8_cad.o(.text.u8x8_cad_ssd13xx_fast_i2c))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = u8x8_cad_ssd13xx_fast_i2c &rArr; u8x8_i2c_data_transfer &rArr; u8x8_byte_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_i2c_data_transfer
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(.text.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
</UL>
<P><STRONG><a name="[219]"></a>u8x8_d_helper_display_init</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, u8x8_display.o(.text.u8x8_d_helper_display_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = u8x8_d_helper_display_init
</UL>
<BR>[Calls]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_gpio_call
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>

<P><STRONG><a name="[21f]"></a>u8x8_d_helper_display_setup_memory</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, u8x8_display.o(.text.u8x8_d_helper_display_setup_memory))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_univision
</UL>

<P><STRONG><a name="[a8]"></a>u8x8_d_ssd1306_128x32_univision</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, u8x8_d_ssd1306_128x32.o(.text.u8x8_d_ssd1306_128x32_univision))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = u8x8_d_ssd1306_128x32_univision &rArr; u8x8_d_ssd1306_128x32_generic &rArr; u8x8_cad_SendSequence
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_setup_memory
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_generic
</UL>
<BR>[Address Reference Count : 1]<UL><LI> u8g2_d_setup.o(.text.u8g2_Setup_ssd1306_i2c_128x32_univision_f)
</UL>
<P><STRONG><a name="[214]"></a>u8x8_gpio_call</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, u8x8_gpio.o(.text.u8x8_gpio_call))
<BR><BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
</UL>

<P><STRONG><a name="[2e]"></a>uart_task</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, usart_app.o(.text.uart_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 1752<LI>Call Chain = uart_task &rArr; handle_sampling_output &rArr; data_storage_write_hidedata &rArr; write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[221]"></a>ui_ftoa_f</STRONG> (Thumb, 228 bytes, Stack size 16 bytes, wououi.o(.text.ui_ftoa_f))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ui_ftoa_f &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f_str
</UL>

<P><STRONG><a name="[186]"></a>ui_ftoa_f_str</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, wououi.o(.text.ui_ftoa_f_str))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ui_ftoa_f_str &rArr; ui_ftoa_f &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>

<P><STRONG><a name="[18e]"></a>ui_ftoa_g</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, wououi.o(.text.ui_ftoa_g))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ui_ftoa_g &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageShow
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
</UL>

<P><STRONG><a name="[18f]"></a>ui_ftoa_g_str</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, wououi.o(.text.ui_ftoa_g_str))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ui_ftoa_g_str &rArr; ui_ftoa_g &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
</UL>

<P><STRONG><a name="[185]"></a>ui_itoa_str</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, wououi.o(.text.ui_itoa_str))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ui_itoa_str &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_ValWinPageDraw
</UL>

<P><STRONG><a name="[222]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[242]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[17a]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageReact
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageInParaInit
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageReact
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageInParaInit
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageReact
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageInParaInit
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageReact
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageInParaInit
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_MsgWinPageInParaInit
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_assert
</UL>

<P><STRONG><a name="[243]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[244]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[224]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[245]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[190]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_filename
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sampling_output
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_command
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_command
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_overlimit
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_sample
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_interactive_input
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;format_hex_output
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WouoUI_SpinWinPageDraw
</UL>

<P><STRONG><a name="[246]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[247]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[225]"></a>__0vsnprintf</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[248]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[249]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[24a]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[1f9]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
</UL>

<P><STRONG><a name="[1ef]"></a>__hardfp_strtof</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, strtof.o(i.__hardfp_strtof))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = __hardfp_strtof &rArr; __strtof_int &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtof_int
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ini_parse_line
</UL>

<P><STRONG><a name="[24b]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[24c]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[24d]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[c8]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[1d3]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
</UL>

<P><STRONG><a name="[1d2]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[1cc]"></a>find_volume</STRONG> (Thumb, 900 bytes, Stack size 56 bytes, ff.o(.text.find_volume))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = find_volume &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
</UL>

<P><STRONG><a name="[1bf]"></a>move_window</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, ff.o(.text.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[1ce]"></a>follow_path</STRONG> (Thumb, 876 bytes, Stack size 80 bytes, ff.o(.text.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1c3]"></a>dir_register</STRONG> (Thumb, 772 bytes, Stack size 64 bytes, ff.o(.text.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[1a5]"></a>get_fat</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, ff.o(.text.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = get_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
</UL>

<P><STRONG><a name="[1a6]"></a>put_fat</STRONG> (Thumb, 264 bytes, Stack size 32 bytes, ff.o(.text.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[1be]"></a>dir_sdi</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, ff.o(.text.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[1bd]"></a>dir_find</STRONG> (Thumb, 432 bytes, Stack size 48 bytes, ff.o(.text.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[1c0]"></a>dir_next</STRONG> (Thumb, 346 bytes, Stack size 40 bytes, ff.o(.text.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[1a4]"></a>create_chain</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, ff.o(.text.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[1d0]"></a>sync_fs</STRONG> (Thumb, 210 bytes, Stack size 32 bytes, ff.o(.text.sync_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sync_fs &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[1c2]"></a>sync_window</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, ff.o(.text.sync_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[9c]"></a>ADC_DMAConvCplt</STRONG> (Thumb, 110 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ADC_DMAConvCplt &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[9d]"></a>ADC_DMAHalfConvCplt</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt))
<BR><BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[9e]"></a>ADC_DMAError</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.ADC_DMAError))
<BR><BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[fe]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 458 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[ff]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 214 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[100]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 186 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
</UL>

<P><STRONG><a name="[101]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 186 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[103]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 200 bytes, Stack size 40 bytes, stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[114]"></a>SD_FindSCR</STRONG> (Thumb, 240 bytes, Stack size 56 bytes, stm32f4xx_hal_sd.o(.text.SD_FindSCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SD_FindSCR &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[130]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 288 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>

<P><STRONG><a name="[147]"></a>UART_SetConfig</STRONG> (Thumb, 230 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(.text.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[a1]"></a>UART_DMAError</STRONG> (Thumb, 380 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.UART_DMAError))
<BR><BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
</UL>
<P><STRONG><a name="[9f]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 350 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
</UL>
<P><STRONG><a name="[a0]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
</UL>
<P><STRONG><a name="[141]"></a>UART_Receive_IT</STRONG> (Thumb, 254 bytes, Stack size 4 bytes, stm32f4xx_hal_uart.o(.text.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; __aeabi_assert &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[a2]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError))
<BR><BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[1c5]"></a>prv_process_btn</STRONG> (Thumb, 646 bytes, Stack size 32 bytes, ebtn.o(.text.prv_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_get_combo_btn_by_key_id
</UL>
<BR>[Called By]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[205]"></a>prv_get_combo_btn_by_key_id</STRONG> (Thumb, 164 bytes, Stack size 0 bytes, ebtn.o(.text.prv_get_combo_btn_by_key_id))
<BR><BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
</UL>

<P><STRONG><a name="[217]"></a>u8x8_i2c_data_transfer</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, u8x8_cad.o(.text.u8x8_i2c_data_transfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = u8x8_i2c_data_transfer &rArr; u8x8_byte_SendByte
</UL>
<BR>[Calls]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_EndTransfer
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_StartTransfer
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_byte_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_ssd13xx_fast_i2c
</UL>

<P><STRONG><a name="[21a]"></a>u8x8_d_ssd1306_128x32_generic</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, u8x8_d_ssd1306_128x32.o(.text.u8x8_d_ssd1306_128x32_generic))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = u8x8_d_ssd1306_128x32_generic &rArr; u8x8_cad_SendSequence
</UL>
<BR>[Calls]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_helper_display_init
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendSequence
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_EndTransfer
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_StartTransfer
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendData
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendArg
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_cad_SendCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;u8x8_d_ssd1306_128x32_univision
</UL>

<P><STRONG><a name="[16a]"></a>WouoUI_CanvasWriteByte</STRONG> (Thumb, 384 bytes, Stack size 24 bytes, wououi_graph.o(.text.WouoUI_CanvasWriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = WouoUI_CanvasWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawPoint
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBMP
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawBoxRightAngle
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxCommon
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_H
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawLine_V
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawASCII
</UL>

<P><STRONG><a name="[183]"></a>WouoUI_ListDrawText_CheckBox</STRONG> (Thumb, 568 bytes, Stack size 88 bytes, wououi_page.o(.text.WouoUI_ListDrawText_CheckBox))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = WouoUI_ListDrawText_CheckBox &rArr; WouoUI_ListAuotCanvasDrawLineTailValTxt &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBox
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_f_str
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memchr
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListAuotCanvasDrawLineTailValTxt
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageShow
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListPageIn
</UL>

<P><STRONG><a name="[182]"></a>WouoUI_ListAuotCanvasDrawLineTailValTxt</STRONG> (Thumb, 210 bytes, Stack size 48 bytes, wououi_page.o(.text.WouoUI_ListAuotCanvasDrawLineTailValTxt))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = WouoUI_ListAuotCanvasDrawLineTailValTxt &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListDrawText_CheckBox
</UL>

<P><STRONG><a name="[18d]"></a>_WouoUI_WaveUpdateRange</STRONG> (Thumb, 736 bytes, Stack size 44 bytes, wououi_page.o(.text._WouoUI_WaveUpdateRange))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = _WouoUI_WaveUpdateRange
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_WavePageReact
</UL>

<P><STRONG><a name="[179]"></a>_WouoUI_ConfWinPageDraw</STRONG> (Thumb, 258 bytes, Stack size 56 bytes, wououi_win.o(.text._WouoUI_ConfWinPageDraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = _WouoUI_ConfWinPageDraw &rArr; WouoUI_CanvasDrawStrAutoNewline &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStrAutoNewline
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageShow
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ConfWinPageIn
</UL>

<P><STRONG><a name="[18c]"></a>_WouoUI_ValWinPageDraw</STRONG> (Thumb, 536 bytes, Stack size 56 bytes, wououi_win.o(.text._WouoUI_ValWinPageDraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = _WouoUI_ValWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_itoa_str
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageShow
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ValWinPageIn
</UL>

<P><STRONG><a name="[18b]"></a>_WouoUI_SpinWinPageDraw</STRONG> (Thumb, 1060 bytes, Stack size 72 bytes, wououi_win.o(.text._WouoUI_SpinWinPageDraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = _WouoUI_SpinWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_GetStrWidth
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawASCII
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_ftoa_g_str
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageShow
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_SpinWinPageIn
</UL>

<P><STRONG><a name="[189]"></a>_WouoUI_listWinPageDraw</STRONG> (Thumb, 234 bytes, Stack size 64 bytes, wououi_win.o(.text._WouoUI_listWinPageDraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = _WouoUI_listWinPageDraw &rArr; WouoUI_CanvasDrawSlideStr &rArr; WouoUI_CanvasDrawASCII &rArr; WouoUI_CanvasWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawRBoxEmpty
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawSlideStr
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_CanvasDrawStr
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageShow
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WouoUI_ListWinPageIn
</UL>

<P><STRONG><a name="[1ff]"></a>test_unhide_conversion</STRONG> (Thumb, 396 bytes, Stack size 72 bytes, usart_app.o(.text.test_unhide_conversion))
<BR><BR>[Stack]<UL><LI>Max Depth = 648<LI>Call Chain = test_unhide_conversion &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1b5]"></a>write_data_to_file</STRONG> (Thumb, 232 bytes, Stack size 680 bytes, data_storage.o(.text.write_data_to_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 1048<LI>Call Chain = write_data_to_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_filename
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_log
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_hidedata
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_overlimit
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_storage_write_sample
</UL>

<P><STRONG><a name="[226]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[223]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[229]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[228]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[ad]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsnprintf)
</UL>
<P><STRONG><a name="[ac]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[c3]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[9a]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[d7]"></a>_local_sscanf</STRONG> (Thumb, 62 bytes, Stack size 64 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
