Dependencies for Project '2025950859', Target '2025950859': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (startup_stm32f429xx.s)(0x684D589E)(--cpu Cortex-M4.fp.sp -g --pd "__MICROLIB SETA 1" --diag_suppress=A1950W

-ID:\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 541"

--pd "GD32F470 SETA 1"

--list startup_stm32f429xx.lst

--xref -o gd32_xifeng\startup_stm32f429xx.o

--depend gd32_xifeng\startup_stm32f429xx.d)
F (../Core/Src/main.c)(0x684FAEB6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/main.o -MMD)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Core\Inc\dma.h)(0x67D552D2)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\rtc.h)(0x684D5898)
I (..\Core\Inc\sdio.h)(0x681EC5F8)
I (..\Core\Inc\spi.h)(0x681CBD00)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\gpio.h)(0x67D552D2)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
I (..\APP\device_id.h)(0x684E4E40)
F (../Core/Src/gpio.c)(0x681CC3C2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/gpio.o -MMD)
I (..\Core\Inc\gpio.h)(0x67D552D2)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/adc.c)(0x681EC618)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/adc.o -MMD)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/dac.c)(0x680CC9A0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/dac.o -MMD)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/dma.c)(0x680DE11C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/dma.o -MMD)
I (..\Core\Inc\dma.h)(0x67D552D2)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/i2c.c)(0x68189578)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/i2c.o -MMD)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/rtc.c)(0x684D5898)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/rtc.o -MMD)
I (..\Core\Inc\rtc.h)(0x684D5898)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/sdio.c)(0x681EC9D0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/sdio.o -MMD)
I (..\Core\Inc\sdio.h)(0x681EC5F8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/spi.c)(0x681D57C4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/spi.o -MMD)
I (..\Core\Inc\spi.h)(0x681CBD00)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/tim.c)(0x6815F962)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/tim.o -MMD)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/usart.c)(0x680DDE72)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/usart.o -MMD)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
F (../Core/Src/stm32f4xx_it.c)(0x6815F962)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_it.o -MMD)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_it.h)(0x6815F962)
F (../Core/Src/stm32f4xx_hal_msp.c)(0x67D552D4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_msp.o -MMD)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../FATFS/Target/bsp_driver_sd.c)(0x681EC5F8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/bsp_driver_sd.o -MMD)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../FATFS/Target/sd_diskio.c)(0x681EC5F8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/sd_diskio.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
F (../FATFS/App/fatfs.c)(0x681EC5F8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/fatfs.o -MMD)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
F (../Middlewares/Third_Party/FatFs/src/diskio.c)(0x67D552B2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/diskio.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
F (../Middlewares/Third_Party/FatFs/src/ff.c)(0x67D552B2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/ff.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
F (../Middlewares/Third_Party/FatFs/src/ff_gen_drv.c)(0x67D552B2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/ff_gen_drv.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
F (../Middlewares/Third_Party/FatFs/src/option/syscall.c)(0x67D552B2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/syscall.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\option\..\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\option\..\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
F (../Middlewares/Third_Party/FatFs/src/option/cc936.c)(0x67D552B2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/cc936.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\option\..\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\option\..\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_adc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_adc_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_ll_adc.o -MMD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_rcc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_rcc_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_flash.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_flash_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_flash_ramfunc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_gpio.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_dma_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_dma.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_pwr.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_pwr_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_cortex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_exti.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_dac.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac_ex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_dac_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_i2c.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_i2c_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_rtc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_rtc_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_sdmmc.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_ll_sdmmc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sd.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_sd.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_mmc.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_mmc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_spi.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_tim.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_tim_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c)(0x67D552C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/stm32f4xx_hal_uart.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (../Core/Src/system_stm32f4xx.c)(0x67D552C4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/system_stm32f4xx.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (..\Components\ebtn\ebtn.c)(0x6815A37C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/ebtn.o -MMD)
I (..\Components\ebtn\ebtn.h)(0x6815A2C6)
I (..\Components\ebtn\bit_array.h)(0x68030432)
F (..\Components\oled\oled.c)(0x6818979C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/oled.o -MMD)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Components\oled\oledfont.h)(0x6819A2DE)
I (..\Core\Inc\i2c.h)(0x68189578)
F (..\Components\u8g2\mui.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/mui.o -MMD)
I (..\Components\u8g2\mui.h)(0x6818ABD2)
F (..\Components\u8g2\mui_u8g2.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/mui_u8g2.o -MMD)
I (..\Components\u8g2\mui.h)(0x6818ABD2)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Components\u8g2\mui_u8g2.h)(0x6818ABD2)
F (..\Components\u8g2\u8g2_arc.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_arc.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_bitmap.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_bitmap.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_box.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_box.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_buffer.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_buffer.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_button.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_button.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_circle.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_circle.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_cleardisplay.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_cleardisplay.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_d_memory.c)(0x68199C74)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_d_memory.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_d_setup.c)(0x68199C24)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_d_setup.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_font.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_font.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_fonts.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_fonts.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_hvline.c)(0x6818ABD2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_hvline.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_input_value.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_input_value.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_intersection.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_intersection.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_kerning.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_kerning.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_line.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_line.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_ll_hvline.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_ll_hvline.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_message.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_message.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_polygon.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_polygon.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_selection_list.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_selection_list.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8g2_setup.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8g2_setup.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8log.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8log.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8log_u8g2.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8log_u8g2.o -MMD)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8log_u8x8.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8log_u8x8.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_8x8.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_8x8.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_byte.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_byte.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_cad.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_cad.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_capture.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_capture.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_d_ssd1306_128x32.c)(0x6818ABD4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_d_ssd1306_128x32.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_debounce.c)(0x6818ABD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_debounce.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_display.c)(0x6818ABD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_display.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_fonts.c)(0x6818ABD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_fonts.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_gpio.c)(0x6818ABD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_gpio.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_input_value.c)(0x6818ABD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_input_value.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_message.c)(0x6818ABD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_message.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_selection_list.c)(0x6818ABD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_selection_list.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_setup.c)(0x6818ABD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_setup.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_string.c)(0x6818ABD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_string.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_u8toa.c)(0x68199D20)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_u8toa.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\u8g2\u8x8_u16toa.c)(0x68199D20)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/u8x8_u16toa.o -MMD)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
F (..\Components\ringbuffer\ringbuffer.c)(0x680DD84C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/ringbuffer.o -MMD)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
F (..\Components\GD25QXX\gd25qxx.c)(0x684D59D8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/gd25qxx.o -MMD)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (..\Components\GD25QXX\lfs.c)(0x681CBD4A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/lfs.o -MMD)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_util.h)(0x681CBC32)
F (..\Components\GD25QXX\lfs_port.c)(0x681CBD4A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/lfs_port.o -MMD)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (..\Components\GD25QXX\lfs_util.c)(0x681CBD4A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/lfs_util.o -MMD)
I (..\Components\GD25QXX\lfs_util.h)(0x681CBC32)
F (..\Components\WouoUI_Page\WouoUI.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/wououi.o -MMD)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
F (..\Components\WouoUI_Page\WouoUI_anim.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/wououi_anim.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
F (..\Components\WouoUI_Page\WouoUI_font.c)(0x6819A2D4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/wououi_font.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
F (..\Components\WouoUI_Page\WouoUI_graph.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/wououi_graph.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
F (..\Components\WouoUI_Page\WouoUI_msg.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/wououi_msg.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
F (..\Components\WouoUI_Page\WouoUI_page.c)(0x6819A498)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/wououi_page.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
F (..\Components\WouoUI_Page\WouoUI_user.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/wououi_user.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
F (..\Components\WouoUI_Page\WouoUI_win.c)(0x6814F710)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/wououi_win.o -MMD)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
F (..\APP\mydefine.h)(0x684D8746)()
F (..\APP\scheduler.c)(0x684E7EEC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/scheduler.o -MMD)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
F (..\APP\usart_app.c)(0x68503293)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/usart_app.o -MMD)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
F (..\APP\led_app.c)(0x68500CF6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/led_app.o -MMD)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
I (..\Core\Inc\gpio.h)(0x67D552D2)
F (..\APP\btn_app.c)(0x684FAEB6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/btn_app.o -MMD)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
I (..\Components\ebtn\ebtn.h)(0x6815A2C6)
I (..\Components\ebtn\bit_array.h)(0x68030432)
I (..\Core\Inc\gpio.h)(0x67D552D2)
F (..\APP\adc_app.c)(0x684E140E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/adc_app.o -MMD)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
F (..\APP\oled_app.c)(0x684FAEB6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/oled_app.o -MMD)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
F (..\APP\flash_app.c)(0x68503093)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/flash_app.o -MMD)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
F (..\APP\rtc_app.c)(0x684D6078)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/rtc_app.o -MMD)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
F (..\APP\system_check.c)(0x68500E5B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/system_check.o -MMD)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
F (..\APP\config_manager.c)(0x684FFBE2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/config_manager.o -MMD)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
F (..\APP\ini_parser.c)(0x684E186A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/ini_parser.o -MMD)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
F (..\APP\sampling_control.c)(0x684FAB4E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/sampling_control.o -MMD)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\data_storage.h)(0x684E63A8)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\ini_parser.h)(0x684E17E0)
F (..\APP\data_storage.c)(0x684FAB4E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/data_storage.o -MMD)
I (..\APP\data_storage.h)(0x684E63A8)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
F (..\APP\device_id.c)(0x684FE8FC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/device_id.o -MMD)
I (..\APP\device_id.h)(0x684E4E40)
I (..\APP\mydefine.h)(0x684D8746)
I (..\Components\WouoUI_Page\WouoUI.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_common.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_conf.h)(0x6819A44A)
I (..\Components\WouoUI_Page\WouoUI_anim.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_graph.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_font.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_page.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_msg.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_win.h)(0x6814F710)
I (..\Components\WouoUI_Page\WouoUI_user.h)(0x6819AADE)
I (..\Components\u8g2\u8g2.h)(0x6818ABD2)
I (..\Components\u8g2\u8x8.h)(0x6818ABD4)
I (..\Core\Inc\i2c.h)(0x68189578)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x67D552C6)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x684D589E)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x67D552C6)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x67D552B0)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x67D552C6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x67D552C6)
I (..\Core\Inc\usart.h)(0x680CCA5E)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\dac.h)(0x680CC9A0)
I (..\Components\oled\oled.h)(0x60BF6F22)
I (..\Components\GD25QXX\lfs.h)(0x681CBC3A)
I (..\Components\GD25QXX\lfs_port.h)(0x681CBC2C)
I (..\Components\GD25QXX\gd25qxx.h)(0x681CBC20)
I (..\APP\scheduler.h)(0x684D8A60)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x6815A636)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x67D552B2)
I (..\FATFS\Target\ffconf.h)(0x684E638C)
I (..\FATFS\Target\bsp_driver_sd.h)(0x681EC5F8)
I (..\FATFS\App\fatfs.h)(0x681EC5F8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x67D552B2)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x67D552B2)
I (..\FATFS\Target\sd_diskio.h)(0x681EC5F8)
I (..\APP\oled_app.h)(0x684E174A)
I (..\APP\adc_app.h)(0x684E12BE)
I (..\APP\led_app.h)(0x684D8744)
I (..\APP\btn_app.h)(0x684E16C8)
I (..\APP\flash_app.h)(0x684D8738)
I (..\APP\usart_app.h)(0x684E5DC0)
I (..\APP\rtc_app.h)(0x684D5DF0)
I (..\APP\system_check.h)(0x684FA78A)
I (..\APP\config_manager.h)(0x684D8B74)
I (..\APP\sampling_control.h)(0x684E5CAE)
I (..\APP\ini_parser.h)(0x684E17E0)
I (..\APP\data_storage.h)(0x684E63A8)
F (..\APP\assert_override.c)(0x684E7CF6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../APP -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/u8g2 -I ../Components/WouoUI_Page -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src

-ID:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o gd32_xifeng/assert_override.o -MMD)
F (../Middlewares/ST/ARM/DSP/Lib/arm_cortexM4l_math.lib)(0x6815A636)()
